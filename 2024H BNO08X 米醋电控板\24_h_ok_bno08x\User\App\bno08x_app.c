#include "bno08x_app.h"
#include "bno08x_hal.h"

extern UART_HandleTypeDef huart1;
extern I2C_HandleTypeDef hi2c1;

float roll, pitch, yaw;
float convert_to_continuous_yaw(float current_yaw);
/**
 * @brief BNO080������������ʼ�������ú���
 * @note ����Ӳ����ʼ������λ�����������õ���������
 * @note �û���ͨ��ע��ѡ�������ò�ͬ����ģ��
 * @retval 0: �ɹ�, -1: ʧ��
 */
int8_t my_bno080_init(void)
{
    my_printf(&huart1, "��ʼ��ʼ��BNO080������...\n");

    // 1. Ӳ����ʼ��
    BNO080_Init(&hi2c1, BNO080_DEFAULT_ADDRESS);
    my_printf(&huart1, "BNO080 I2C��ʼ�����\n");

    // 2. Ӳ����λ���Ƽ�ʹ�ã����ɿ���
    if (BNO080_HardwareReset() == 0) {
        my_printf(&huart1, "BNO080Ӳ����λ�ɹ�\n");
    } else {
        my_printf(&huart1, "BNO080Ӳ����λʧ�ܣ�����������λ\n");
        // ���÷�����������λ
        softReset();
        HAL_Delay(100);
        my_printf(&huart1, "BNO080������λ���\n");
    }

    // 3. �������������ã��û���ѡ����ע��/ȡ��ע�ͣ�
    enableRotationVector(100);        // ������ת������100ms���
    my_printf(&huart1, "��������ת�������� (100ms)\n");

    // enableAccelerometer(50);          // ���ü��ٶȼƣ�50ms���
    // my_printf(&huart1, "�����ü��ٶȼƱ��� (50ms)\n");

    // enableGyro(50);                   // ���������ǣ�50ms���
    // my_printf(&huart1, "�����������Ǳ��� (50ms)\n");

    // enableMagnetometer(100);          // ���ô����ƣ�100ms���
    // my_printf(&huart1, "�����ô����Ʊ��� (100ms)\n");

    // enableLinearAccelerometer(50);    // �������Լ��ٶȣ�50ms���
    // my_printf(&huart1, "���������Լ��ٶȱ��� (50ms)\n");

    // enableStepCounter(1000);          // ���ò���������1000ms���
    // my_printf(&huart1, "�����ò��������� (1000ms)\n");

    // 4. �߼��������ã���ѡ��
    // enableGameRotationVector(20);     // ��Ϸģʽ��ת������20ms���
    // my_printf(&huart1, "��������Ϸ��ת���� (20ms)\n");

    // enableStabilityClassifier(500);   // �ȶ��Է�������500ms���
    // my_printf(&huart1, "�������ȶ��Է����� (500ms)\n");

    HAL_Delay(200); // �ȴ�������������Ч
    my_printf(&huart1, "BNO080��������ʼ����ɣ�\n");

    return 0; // ��ʼ���ɹ�
}

uint8_t first_flat = 0;
float frist_yaw = 0;
/**
 * @brief BNO080���������ݶ�ȡ�ʹ�������
 * @note �����ڵ�����ϵͳ������50-100msִ������
 * @note �û���ͨ��ע��ѡ�������ò�ͬ�������
 */
void bno080_task(void)
{

    // ����Ƿ���������
    if (dataAvailable()) {

        // 1. ��Ԫ������̬���ݣ��������ܣ�
        float quat_i = getQuatI();
        float quat_j = getQuatJ();
        float quat_k = getQuatK();
        float quat_real = getQuatReal();
        QuaternionToEulerAngles(quat_i, quat_j, quat_k, quat_real, &roll, &pitch, &yaw);
				if(first_flat == 0)
				{
					first_flat = 1;
					frist_yaw = yaw;
				}
				yaw = yaw-frist_yaw;
	  
//        my_printf(&huart1, "Euler: %.2f, %.2f, %.2f\n", roll, pitch, yaw);

        // 2. ���ٶ����ݣ���ѡ��
        // float accel_x = getAccelX();
        // float accel_y = getAccelY();
        // float accel_z = getAccelZ();
        // my_printf(&huart1, "Accel: %.3f, %.3f, %.3f g\n", accel_x, accel_y, accel_z);

        // 3. ���������ݣ���ѡ��
        // float gyro_x = getGyroX();
        // float gyro_y = getGyroY();
        // float gyro_z = getGyroZ();
        // my_printf(&huart1, "Gyro: %.3f, %.3f, %.3f rad/s\n", gyro_x, gyro_y, gyro_z);

        // 4. ���������ݣ���ѡ��
        // float mag_x = getMagX();
        // float mag_y = getMagY();
        // float mag_z = getMagZ();
        // my_printf(&huart1, "Mag: %.1f, %.1f, %.1f ��T\n", mag_x, mag_y, mag_z);

        // 5. ���ȼ�أ������ã�
        // uint8_t quat_accuracy = getQuatAccuracy();
        // uint8_t accel_accuracy = getAccelAccuracy();
        // uint8_t gyro_accuracy = getGyroAccuracy();
        // uint8_t mag_accuracy = getMagAccuracy();
        // my_printf(&huart1, "Accuracy: Q=%d A=%d G=%d M=%d\n",
        //           quat_accuracy, accel_accuracy, gyro_accuracy, mag_accuracy);

        // 6. �߼��������ݣ���ѡ��
        // uint16_t steps = getStepCount();
        // uint8_t stability = getStabilityClassifier();
        // my_printf(&huart1, "Steps: %d, Stability: %d\n", steps, stability);
    }
}

float get_roll(void)
{
	return roll;
}

float get_pitch(void)
{
	return pitch;
}

float get_yaw(void)
{
	float YAW = convert_to_continuous_yaw(yaw);
	return YAW;
}

// ʹ�þ�̬������������һ�ε�״̬
float g_last_yaw = 0.0f;
int g_revolution_count = 0;
bool g_is_yaw_initialized = false;
/**
 * @brief ��һ����[-180, 180]��Χ�ڵ�yaw�Ƕ�ת��Ϊ�����ĽǶ�ֵ��
 * 
 * @param current_yaw �Ӵ�������ȡ�ĵ�ǰyawֵ (-180 to 180)��
 * @return float ������yaw�Ƕ�ֵ (���� 370, -450 ��)��
 */
float convert_to_continuous_yaw(float current_yaw) 
{
    // ����һ����ֵ����⡰���䡱�����ֵӦ�ô���180��ͨ��ȡ270��300�Ƚϰ�ȫ��
    const float WRAP_AROUND_THRESHOLD = 300.0f;

    // �״ε���ʱ���г�ʼ��
    if (!g_is_yaw_initialized) {
        g_last_yaw = current_yaw;
        g_is_yaw_initialized = true;
        g_revolution_count = 0;
    }

    // �������ϴζ����Ĳ���
    float diff = current_yaw - g_last_yaw;

    // ����Ƿ����ˡ����䡱
    if (diff > WRAP_AROUND_THRESHOLD) {
        // �����Ƕ��������Ƕ� (����, �� 170�� �� -175��), ʵ��������ת, Ȧ��Ӧ������
        // ��ʱ diff �ӽ� -360 (���� -175 - 170 = -345)
        // ����߼��������Ǵ�-180���䵽+180�������˵��������ת����
        g_revolution_count--;
    } else if (diff < -WRAP_AROUND_THRESHOLD) {
        // �Ӹ��Ƕ��������Ƕ� (����, �� -170�� �� 175��), ʵ��������ת, Ȧ��Ӧ�ü�С
        // ��ʱ diff �ӽ� 360 (���� 175 - (-170) = 345)
        // ����߼��������Ǵ�+180���䵽-180�������˵��������ת����
        g_revolution_count++;
    }

    // �����ϴε�yawֵ�Ա��´ε���
    g_last_yaw = current_yaw;

    // ����������yawֵ
    float continuous_yaw = current_yaw + (float)g_revolution_count * 360.0f;

    return continuous_yaw;
}
